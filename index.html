<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/logo.png"/>
    <meta content="yes" name="mobile-web-app-capable"/>
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover"
    />
    <!-- <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests"> -->
    <meta http-equiv="Cache" content="no-cache">
    <meta http-equiv="Cache-Control" content="no-store, no-cache">
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Pragma" content="no-cache">
    <title></title>
  </head>

  <body>
    <div id="app-chat" class="h-full com-app-wrap">
      <style>
        .loading-wrap {
          display: flex;
          justify-content: center;
          align-items: center;
          flex: 1;
          height: 100vh;
        }

        .balls {
          width: 4em;
          display: flex;
          flex-flow: row nowrap;
          align-items: center;
          justify-content: space-between;
        }

        .balls div {
          width: 0.8em;
          height: 0.8em;
          border-radius: 50%;
          background-color: #3B66F5;
        }

        .balls div:nth-of-type(1) {
          transform: translateX(-100%);
          animation: left-swing 0.5s ease-in alternate infinite;
        }

        .balls div:nth-of-type(3) {
          transform: translateX(-95%);
          animation: right-swing 0.5s ease-out alternate infinite;
        }

        @keyframes left-swing {
          50%,
          100% {
            transform: translateX(95%);
          }
        }

        @keyframes right-swing {
          50% {
            transform: translateX(-95%);
          }

          100% {
            transform: translateX(100%);
          }
        }

        @media (prefers-color-scheme: dark) {
          body {
            background: #121212;
          }
        }
      </style>
      <div class="loading-wrap">
        <div class="balls">
          <div></div>
          <div></div>
          <div></div>
        </div>
      </div>
    </div>
  </body>
</html>
