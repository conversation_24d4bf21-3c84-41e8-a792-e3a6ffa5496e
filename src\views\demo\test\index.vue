<template>
    <div class="document-editor-wrapper">
        <div class="document-editor-container">
            <div id="documentEditor" v-show="isComponentReady"></div>
            <div v-show="!isComponentReady" class="loading">
                <div class="spinner"></div>
                正在加载文档编辑器...
                <!-- 使用私有化部署的 OnlyOffice API -->
            </div>
        </div>
        <div class="suggestion-panel">
            <div class="suggestion-list">
                <a-button type="primary" @click="testJumpPage(10)">跳转页码</a-button>
                <a-button type="primary" @click="testHighlight(
                    {
                    page:2,position:[{
                    page: 3,
                    x1: 77, y1: 249, x2: 536, y2: 279
                }]})">跳转2页，第4页有高亮选区</a-button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { message } from 'ant-design-vue';
import CryptoJS from 'crypto-js';

// 声明 OnlyOffice 编辑器脚本环境中的全局对象
declare const Asc: any;
declare const Api: any;
declare const DocsAPI: any;

const isComponentReady = ref(false);
const docEditorInstance = ref<any>(null);
const connector = ref<any>(null);
// OnlyOffice 配置 - 与 docs/index.html 保持一致
const config = reactive({
    "height": "100%",
    "width": "100%",
    "document": {
        "fileType": "docx",
        "key": generateDocumentKey(),
        "title": "测试文件.docx",
        "url": "http://************:8090/files/test.docx",
    },
    "editorConfig": {
        "customization": {
            "autosave": false,
            "compactToolbar": true,
            "forcesave": true,
            "toolbarNoTabs": true,
            "help": false,
            "compactHeader": true,
            "hideRightMenu": true,
        },
        "mode": "edit",
        "callbackUrl": "http://172.16.0.198:18000/callback",
        "lang": "zh-CN",
    },
    "events": {
        "onDocumentReady": function () {
            console.log('OnlyOffice 应用已准备就绪');

            // 确保 docEditor 已创建
            if (!docEditorInstance.value) {
                console.error('docEditor 未初始化');
                return;
            }

            // 检查 createConnector 是否可用
            if (typeof docEditorInstance.value.createConnector !== 'function') {
                console.error('createConnector 不可用 —— 检查 api.js 版本 / Document Server edition / license');
                return;
            }

            // 在编辑器就绪后创建 connector
            connector.value = docEditorInstance.value.createConnector();
        },
        "onDocumentStateChange": function (event: any) {
            console.log('文档状态变更:', event);
        },
        "onError": function (event: any) {
            console.error('OnlyOffice错误:', event);
        },
        "onInfo": function (event: any) {
            console.log('OnlyOffice信息:', event);
        }
    },
    "token": "" as string // JWT token
});

// 生成文档密钥
function generateDocumentKey() {
    return 'demo_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}


// 页码跳转功能
const testJumpPage = (page: number) => {
    if (!connector.value) {
        message.error('编辑器连接器未就绪');
        return;
    }

    // 设置跳转参数
    (window as any).Asc = (window as any).Asc || {};
    (window as any).Asc.scope = (window as any).Asc.scope || {};
    (window as any).Asc.scope.targetPage = page;

    // 执行跳转命令
    connector.value.callCommand(function () {
        var pageNum = parseInt(Asc.scope.targetPage, 10);
        var oDocument = Api.GetDocument();

        // 跳转到指定页面
        if (oDocument && oDocument.GoToPage) {
            oDocument.GoToPage(pageNum - 1); // OnlyOffice 页码从0开始
        }
    });

    console.log(`跳转到第${page}页`);
};

// 高亮选区功能 - 基于坐标的区域高亮
const testHighlight = (data: any) => {
    if (!connector.value) {
        message.error('编辑器连接器未就绪');
        return;
    }

    // 处理按钮传入的数据格式: {page:2, position:[{page: 3, x1: 77, y1: 249, x2: 536, y2: 279}]}
    const { page, position } = data;

    // 先跳转到指定页面
    if (page) {
        (window as any).Asc = (window as any).Asc || {};
        (window as any).Asc.scope = (window as any).Asc.scope || {};
        (window as any).Asc.scope.jumpPage = page;

        connector.value.callCommand(function () {
            var pageNum = parseInt(Asc.scope.jumpPage, 10);
            var oDocument = Api.GetDocument();
            if (oDocument && oDocument.GoToPage) {
                oDocument.GoToPage(pageNum - 1);
            }
        });
    }

    // 处理高亮选区 - 在指定页面创建高亮区域
    if (position && position.length > 0) {
        const pos = position[0]; // 取第一个位置信息
        const { page: highlightPage, x1, y1, x2, y2 } = pos;

        // 设置高亮参数
        Object.assign((window as any).Asc.scope, {
            highlightPage,
            x1, y1, x2, y2,
            textColor: 'red',        // 文字颜色：红色
            backgroundColor: 'red'    // 背景色：透明红色
        });

        // 在指定页面创建高亮选区
        connector.value.callCommand(function () {
            var targetPage = parseInt(Asc.scope.highlightPage, 10);
            var x1 = parseFloat(Asc.scope.x1);
            var y1 = parseFloat(Asc.scope.y1);
            var x2 = parseFloat(Asc.scope.x2);
            var y2 = parseFloat(Asc.scope.y2);
            var textColor = 'red';           // 文字颜色：红色
            var backgroundColor = 'red';     // 背景色：红色（半透明效果）

            var oDocument = Api.GetDocument();

            // 跳转到要创建高亮的页面
            if (oDocument && oDocument.GoToPage) {
                oDocument.GoToPage(targetPage - 1);
            }

            // 在指定页面创建高亮选区
            // 由于 OnlyOffice API 不直接支持像素坐标，这里使用文档结构方式模拟
            try {
                // 方法1：尝试通过搜索特定文本来定位并高亮
                var searchResults = oDocument.Search("评审", true);
                if (searchResults && searchResults.length > 0) {
                    // 设置文字颜色为红色
                    searchResults[0].SetColor(255, 0, 0); // RGB红色
                    // 设置背景高亮为红色（半透明效果）
                    searchResults[0].SetHighlight(backgroundColor);

                    // 获取包含该文本的段落
                    var element = searchResults[0].GetParent();
                    if (element && element.SetLeftBorder) {
                        // 设置淡红色边框
                        element.SetLeftBorder("single", 12, 0, 255, 200, 200);
                        element.SetRightBorder("single", 12, 0, 255, 200, 200);
                        element.SetTopBorder("single", 12, 0, 255, 200, 200);
                        element.SetBottomBorder("single", 12, 0, 255, 200, 200);
                    }
                } else {
                    // 方法2：如果搜索不到，则高亮页面上的某个段落作为示例
                    var elementCount = oDocument.GetElementsCount();
                    // 根据页面估算元素位置（简化算法）
                    var estimatedIndex = Math.floor((targetPage - 1) * 10) + 2;
                    var targetElementIndex = Math.min(estimatedIndex, elementCount - 1);

                    var element = oDocument.GetElement(targetElementIndex);
                    if (element && element.GetClassType() === "paragraph") {
                        var text = element.GetText();
                        if (text && text.length > 0) {
                            // 创建选区范围（模拟坐标区域）
                            var highlightLength = Math.min(20, text.length);
                            var range = element.GetRange(0, highlightLength);

                            // 设置文字颜色为红色
                            range.SetColor(255, 0, 0); // RGB红色
                            // 设置背景高亮为红色（半透明效果）
                            range.SetHighlight(backgroundColor);

                            // 设置淡红色边框标识高亮区域
                            element.SetLeftBorder("single", 12, 0, 255, 200, 200);
                            element.SetRightBorder("single", 12, 0, 255, 200, 200);
                            element.SetTopBorder("single", 12, 0, 255, 200, 200);
                            element.SetBottomBorder("single", 12, 0, 255, 200, 200);
                        }
                    }
                }
            } catch (e) {
                console.warn('创建高亮选区失败:', e);
            }
        });
    }

    console.log(`跳转到第${page}页，然后在第${position[0]?.page}页创建高亮选区，坐标范围: (${position[0]?.x1},${position[0]?.y1}) 到 (${position[0]?.x2},${position[0]?.y2})`);
};


// JWT令牌生成
const setupJWT = () => {
    const payload = {
        document: config.document,
        editorConfig: config.editorConfig,
        height: config.height,
        width: config.width
    };

    // 使用与docs/index.html相同的密钥
    const secret = 'jwt@bos6688';
    config.token = createJWT(payload, secret);
};

/**
 * 生成 ONLYOFFICE 需要的 JWT（HS256）
 * @param {object} payload  ONLYOFFICE 配置对象
 * @param {string} secret   与 ONLYOFFICE 服务端一致的密钥
 * @returns {string} 标准 JWT
 */
const createJWT = (payload: any, secret: string): string => {
    if (!secret) return '';

    const header = { alg: 'HS256', typ: 'JWT' };
    const headerEnc = base64url(JSON.stringify(header));
    const payloadEnc = base64url(JSON.stringify(payload));

    const signature = CryptoJS.HmacSHA256(`${headerEnc}.${payloadEnc}`, secret);
    const signatureEnc = signature.toString(CryptoJS.enc.Base64)
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');

    return `${headerEnc}.${payloadEnc}.${signatureEnc}`;
};

/**
 * Base64URL编码
 * @param {string} data 要编码的数据
 * @returns {string} Base64URL编码后的字符串
 */
const base64url = (data: string): string => {
    // 使用CryptoJS进行Base64编码
    const base64 = CryptoJS.enc.Base64.stringify(
        CryptoJS.enc.Utf8.parse(data)
    );
    
    // 转换为Base64URL格式
    return base64
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
}; 

// 初始化编辑器
async function initEditor() {
    // 生成JWT令牌
    setupJWT();

    // 清除之前的编辑器实例
    if (docEditorInstance.value) {
        docEditorInstance.value.destroyEditor();
    }

    // 清空编辑器容器
    const container = document.getElementById('documentEditor');
    if (container) {
        container.innerHTML = '';
    }

    // 初始化新的编辑器
    docEditorInstance.value = new DocsAPI.DocEditor("documentEditor", config);

    isComponentReady.value = true;
}

onMounted(async () => {
    // 检查API是否加载成功
    if (typeof DocsAPI !== 'undefined') {
        await initEditor();
    } else {
        console.log('OnlyOffice API加载失败，请检查网络连接');
        message.error('OnlyOffice API加载失败，请检查网络连接');
    }
});

// 组件卸载时清理监听器
onUnmounted(() => { 
});
</script>

<style scoped>
.document-editor-wrapper {
    display: flex;
    width: 100%;
    height: 100vh;
}

.document-editor-container {
    flex: 1;
    height: 100vh;
    position: relative;
    border-right: 1px solid #e8e8e8;
}

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    font-size: 16px;
    color: #666;
}

#docEditor {
    width: 100%;
    height: 100%;
}

.suggestion-panel {
    width: 350px;
    height: 100vh;
    overflow-y: auto;
    background-color: #f9f9f9;
    padding: 16px;
    box-sizing: border-box;
}

.suggestion-panel h3 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 18px;
    color: #333;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 8px;
} 

.suggestion-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.suggestion-item {
    background-color: white;
    border-radius: 4px;
    padding: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.3s;
    border: 1px solid #e8e8e8;
}

.suggestion-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.suggestion-item.active {
    border-color: #1890ff;
    background-color: #e6f7ff;
}

.suggestion-content {
    margin-bottom: 12px;
}

.suggestion-title {
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.suggestion-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: normal;
}

.suggestion-status.pending {
    background-color: #fff7e6;
    color: #fa8c16;
    border: 1px solid #ffd591;
}

.suggestion-status.accepted {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.suggestion-status.rejected {
    background-color: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
}

.suggestion-text {
    font-size: 14px;
    color: #666;
}

.original-text, .suggested-text {
    margin-bottom: 4px;
    word-break: break-all;
}

.original-text {
    color: #ff4d4f;
    text-decoration: line-through;
}

.suggested-text {
    color: #52c41a;
}

.location {
    font-size: 12px;
    color: #999;
    margin-top: 8px;
    line-height: 1.4;
}

.location > div {
    margin-bottom: 2px;
}

.suggestion-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.accept-btn, .reject-btn {
    padding: 4px 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
}

.accept-btn {
    background-color: #52c41a;
    color: white;
}

.accept-btn:hover {
    background-color: #73d13d;
}

.reject-btn {
    background-color: #f5f5f5;
    color: #666;
}

.reject-btn:hover {
    background-color: #e8e8e8;
}
</style>

