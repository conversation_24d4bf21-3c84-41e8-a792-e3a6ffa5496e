<template>
    <div class="document-editor-wrapper">
        <div class="document-editor-container">
            <div id="documentEditor" v-show="isComponentReady"></div>
            <!-- 坐标高亮覆盖层 -->
            <div
                v-for="highlight in highlights"
                :key="highlight.id"
                class="coordinate-highlight"
                :style="{
                    left: highlight.x1 + 'px',
                    top: highlight.y1 + 'px',
                    width: (highlight.x2 - highlight.x1) + 'px',
                    height: (highlight.y2 - highlight.y1) + 'px',
                    display: highlight.visible ? 'block' : 'none'
                }"
            ></div>
            <div v-show="!isComponentReady" class="loading">
                <div class="spinner"></div>
                正在加载文档编辑器...
                <!-- 使用私有化部署的 OnlyOffice API -->
            </div>
        </div>
        <div class="suggestion-panel">
            <div class="suggestion-list">
                <a-button type="primary" @click="testJumpPage(10)">跳转页码</a-button>
                <a-button type="primary" @click="testHighlight(
                    {
                    page:2,position:[{
                    page: 3,
                    x1: 77, y1: 249, x2: 536, y2: 279
                }]})">跳转2页，第4页有高亮选区</a-button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { message } from 'ant-design-vue';
import CryptoJS from 'crypto-js';

// 声明 OnlyOffice 编辑器脚本环境中的全局对象
declare const Asc: any;
declare const Api: any;
declare const DocsAPI: any;

const isComponentReady = ref(false);
const docEditorInstance = ref<any>(null);
const connector = ref<any>(null);

// 坐标高亮数据
const highlights = ref<Array<{
    id: string;
    x1: number;
    y1: number;
    x2: number;
    y2: number;
    visible: boolean;
    page: number;
}>>([]);
// OnlyOffice 配置 - 与 docs/index.html 保持一致
const config = reactive({
    "height": "100%",
    "width": "100%",
    "document": {
        "fileType": "docx",
        "key": generateDocumentKey(),
        "title": "测试文件.docx",
        "url": "http://************:8090/files/test.docx",
    },
    "editorConfig": {
        "customization": {
            "autosave": false,
            "compactToolbar": true,
            "forcesave": true,
            "toolbarNoTabs": true,
            "help": false,
            "compactHeader": true,
            "hideRightMenu": true,
        },
        "mode": "edit",
        "callbackUrl": "http://172.16.0.198:18000/callback",
        "lang": "zh-CN",
    },
    "events": {
        "onDocumentReady": function () {
            console.log('OnlyOffice 应用已准备就绪');

            // 确保 docEditor 已创建
            if (!docEditorInstance.value) {
                console.error('docEditor 未初始化');
                return;
            }

            // 检查 createConnector 是否可用
            if (typeof docEditorInstance.value.createConnector !== 'function') {
                console.error('createConnector 不可用 —— 检查 api.js 版本 / Document Server edition / license');
                return;
            }

            // 在编辑器就绪后创建 connector
            connector.value = docEditorInstance.value.createConnector();
        },
        "onDocumentStateChange": function (event: any) {
            console.log('文档状态变更:', event);
        },
        "onError": function (event: any) {
            console.error('OnlyOffice错误:', event);
        },
        "onInfo": function (event: any) {
            console.log('OnlyOffice信息:', event);
        }
    },
    "token": "" as string // JWT token
});

// 生成文档密钥
function generateDocumentKey() {
    return 'demo_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}


// 页码跳转功能 - 参考 docs/index.html 实现
const testJumpPage = (page: number) => {
    if (!connector.value) {
        message.error('编辑器连接器未就绪');
        return;
    }

    // 设置跳转参数
    (window as any).Asc = (window as any).Asc || {};
    (window as any).Asc.scope = (window as any).Asc.scope || {};
    (window as any).Asc.scope.targetPage = page;

    // 执行跳转命令
    connector.value.callCommand(function () {
        var pageNum = parseInt(Asc.scope.targetPage, 10);

        // 尝试多种跳转方式
        try {
            // 方式1：通过文档对象跳转
            var oDocument = Api.GetDocument();
            if (oDocument && oDocument.GoToPage) {
                oDocument.GoToPage(pageNum - 1);
                return;
            }

            // 方式2：通过视图对象跳转
            var oView = Api.GetActiveView && Api.GetActiveView();
            if (oView && oView.GoToPage) {
                oView.GoToPage(pageNum - 1);
                return;
            }

            // 方式3：通过应用对象跳转
            if (Api.asc_docs_api && Api.asc_docs_api.GoToPage) {
                Api.asc_docs_api.GoToPage(pageNum - 1);
                return;
            }

            console.warn('所有跳转方法都不可用');

            // 调试信息：输出可用的 API 方法
            console.log('可用的 API 对象:', {
                Api: typeof Api !== 'undefined' ? Object.keys(Api) : 'undefined',
                oDocument: oDocument ? Object.keys(oDocument).filter(key => key.includes('Page') || key.includes('Go')) : 'null',
                oView: oView ? Object.keys(oView).filter(key => key.includes('Page') || key.includes('Go')) : 'null'
            });
        } catch (e) {
            console.error('页面跳转失败:', e);
        }
    });

    console.log(`尝试跳转到第${page}页`);

    // 额外尝试：使用浏览器滚动作为备选方案
    setTimeout(() => {
        const iframe = document.querySelector('#documentEditor iframe') as HTMLIFrameElement;
        if (iframe && iframe.contentDocument) {
            try {
                const iframeDoc = iframe.contentDocument;

                // 动态计算页面高度
                const pages = iframeDoc.querySelectorAll('.page, [data-page], .document-page, .asc-page');
                if (pages.length > 0) {
                    const firstPage = pages[0] as HTMLElement;
                    const pageHeight = firstPage.offsetHeight || firstPage.clientHeight;
                    const scrollTop = (page - 1) * pageHeight;

                    if (iframeDoc.documentElement) {
                        iframeDoc.documentElement.scrollTop = scrollTop;
                        console.log(`使用浏览器滚动到第${page}页，页面高度: ${pageHeight}px，滚动位置: ${scrollTop}px`);
                    }
                } else {
                    // 如果找不到页面元素，尝试根据文档总高度估算
                    const totalHeight = iframeDoc.documentElement.scrollHeight;
                    const estimatedPageHeight = totalHeight / 10; // 假设有10页
                    const scrollTop = (page - 1) * estimatedPageHeight;

                    if (iframeDoc.documentElement) {
                        iframeDoc.documentElement.scrollTop = scrollTop;
                        console.log(`使用估算高度滚动到第${page}页，估算页面高度: ${estimatedPageHeight}px，滚动位置: ${scrollTop}px`);
                    }
                }
            } catch (e) {
                console.warn('浏览器滚动失败:', e);
            }
        }
    }, 500);
};

// 方案1：使用 CSS 覆盖层实现坐标高亮
const testHighlight = (data: any) => {
    // 处理按钮传入的数据格式: {page:2, position:[{page: 3, x1: 77, y1: 249, x2: 536, y2: 279}]}
    const { page, position } = data;

    // 先跳转到指定页面
    if (page) {
        testJumpPage(page);
    }

    // 延迟创建高亮，等待页面跳转完成
    setTimeout(() => {
        if (position && position.length > 0) {
            const pos = position[0];
            const { page: highlightPage, x1, y1, x2, y2 } = pos;

            // 清除之前的高亮
            highlights.value = [];

            // 计算实际的坐标位置
            let actualX1 = x1;
            let actualY1 = y1;
            let actualX2 = x2;
            let actualY2 = y2;

            // 动态计算页面偏移
            const iframe = document.querySelector('#documentEditor iframe') as HTMLIFrameElement;
            if (iframe && iframe.contentDocument) {
                try {
                    const iframeDoc = iframe.contentDocument;
                    const pages = iframeDoc.querySelectorAll('.page, [data-page], .document-page, .asc-page');

                    if (pages.length > 0 && highlightPage <= pages.length) {
                        // 方法1：精确计算 - 累加前面所有页面的高度
                        let totalOffset = 0;
                        for (let i = 0; i < highlightPage - 1; i++) {
                            const pageElement = pages[i] as HTMLElement;
                            totalOffset += pageElement.offsetHeight || pageElement.clientHeight;
                        }

                        actualY1 = y1 + totalOffset;
                        actualY2 = y2 + totalOffset;

                        console.log(`页面${highlightPage}精确坐标调整: 累计偏移+${totalOffset}px`);
                    } else if (pages.length > 0) {
                        // 方法2：使用第一页高度估算
                        const firstPage = pages[0] as HTMLElement;
                        const pageHeight = firstPage.offsetHeight || firstPage.clientHeight;
                        const pageOffset = (highlightPage - 1) * pageHeight;

                        actualY1 = y1 + pageOffset;
                        actualY2 = y2 + pageOffset;

                        console.log(`页面${highlightPage}估算坐标调整: 页面高度${pageHeight}px，Y偏移+${pageOffset}px`);
                    } else {
                        // 方法3：根据文档总高度估算
                        const totalHeight = iframeDoc.documentElement.scrollHeight;
                        const estimatedPageHeight = totalHeight / 10; // 假设有10页
                        const pageOffset = (highlightPage - 1) * estimatedPageHeight;

                        actualY1 = y1 + pageOffset;
                        actualY2 = y2 + pageOffset;

                        console.log(`页面${highlightPage}文档高度估算: 总高度${totalHeight}px，估算页面高度${estimatedPageHeight}px，Y偏移+${pageOffset}px`);
                    }
                } catch (e) {
                    console.warn('无法计算页面偏移，使用原始坐标:', e);
                }
            }

            // 添加高亮区域到数组
            const highlightId = `highlight_${Date.now()}`;
            highlights.value.push({
                id: highlightId,
                x1: actualX1,
                y1: actualY1,
                x2: actualX2,
                y2: actualY2,
                visible: true,
                page: highlightPage
            });

            console.log(`在第${highlightPage}页坐标(${actualX1},${actualY1})-(${actualX2},${actualY2})创建CSS高亮覆盖层`);
        }
    }, 1500); // 延迟1.5秒等待跳转完成

};


// JWT令牌生成
const setupJWT = () => {
    const payload = {
        document: config.document,
        editorConfig: config.editorConfig,
        height: config.height,
        width: config.width
    };

    // 使用与docs/index.html相同的密钥
    const secret = 'jwt@bos6688';
    config.token = createJWT(payload, secret);
};

/**
 * 生成 ONLYOFFICE 需要的 JWT（HS256）
 * @param {object} payload  ONLYOFFICE 配置对象
 * @param {string} secret   与 ONLYOFFICE 服务端一致的密钥
 * @returns {string} 标准 JWT
 */
const createJWT = (payload: any, secret: string): string => {
    if (!secret) return '';

    const header = { alg: 'HS256', typ: 'JWT' };
    const headerEnc = base64url(JSON.stringify(header));
    const payloadEnc = base64url(JSON.stringify(payload));

    const signature = CryptoJS.HmacSHA256(`${headerEnc}.${payloadEnc}`, secret);
    const signatureEnc = signature.toString(CryptoJS.enc.Base64)
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');

    return `${headerEnc}.${payloadEnc}.${signatureEnc}`;
};

/**
 * Base64URL编码
 * @param {string} data 要编码的数据
 * @returns {string} Base64URL编码后的字符串
 */
const base64url = (data: string): string => {
    // 使用CryptoJS进行Base64编码
    const base64 = CryptoJS.enc.Base64.stringify(
        CryptoJS.enc.Utf8.parse(data)
    );
    
    // 转换为Base64URL格式
    return base64
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
}; 

// 初始化编辑器
async function initEditor() {
    // 生成JWT令牌
    setupJWT();

    // 清除之前的编辑器实例
    if (docEditorInstance.value) {
        docEditorInstance.value.destroyEditor();
    }

    // 清空编辑器容器
    const container = document.getElementById('documentEditor');
    if (container) {
        container.innerHTML = '';
    }

    // 初始化新的编辑器
    docEditorInstance.value = new DocsAPI.DocEditor("documentEditor", config);

    isComponentReady.value = true;
}

onMounted(async () => {
    // 检查API是否加载成功
    if (typeof DocsAPI !== 'undefined') {
        await initEditor();
    } else {
        console.log('OnlyOffice API加载失败，请检查网络连接');
        message.error('OnlyOffice API加载失败，请检查网络连接');
    }
});

// 组件卸载时清理监听器
onUnmounted(() => { 
});
</script>

<style scoped>
/* 文档编辑器容器样式 */
.document-editor-container {
    width: 100%;
    height: 600px;
    border: 1px solid #ddd;
    position: relative;
}

#documentEditor {
    width: 100%;
    height: 100%;
}

/* 坐标高亮覆盖层样式 - 实现图片中的红色高亮效果 */
.coordinate-highlight {
    position: absolute;
    background-color: rgba(255, 0, 0, 0.2); /* 透明红色背景 */
    border: 2px solid #ff0000; /* 红色边框 */
    pointer-events: none; /* 不阻挡鼠标事件 */
    z-index: 1000; /* 确保在最上层 */
    box-sizing: border-box;
    border-radius: 2px;
}

.coordinate-highlight::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 0, 0, 0.1); /* 额外的透明红色层 */
}

.loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #666;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 2s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.test-buttons {
    margin: 20px 0;
    display: flex;
    gap: 10px;
}

.document-editor-wrapper {
    display: flex;
    width: 100%;
    height: 100vh;
}

.document-editor-container {
    flex: 1;
    height: 100vh;
    position: relative;
    border-right: 1px solid #e8e8e8;
}

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    font-size: 16px;
    color: #666;
}

#docEditor {
    width: 100%;
    height: 100%;
}

.suggestion-panel {
    width: 350px;
    height: 100vh;
    overflow-y: auto;
    background-color: #f9f9f9;
    padding: 16px;
    box-sizing: border-box;
}

.suggestion-panel h3 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 18px;
    color: #333;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 8px;
} 

.suggestion-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.suggestion-item {
    background-color: white;
    border-radius: 4px;
    padding: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.3s;
    border: 1px solid #e8e8e8;
}

.suggestion-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.suggestion-item.active {
    border-color: #1890ff;
    background-color: #e6f7ff;
}

.suggestion-content {
    margin-bottom: 12px;
}

.suggestion-title {
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.suggestion-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: normal;
}

.suggestion-status.pending {
    background-color: #fff7e6;
    color: #fa8c16;
    border: 1px solid #ffd591;
}

.suggestion-status.accepted {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.suggestion-status.rejected {
    background-color: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
}

.suggestion-text {
    font-size: 14px;
    color: #666;
}

.original-text, .suggested-text {
    margin-bottom: 4px;
    word-break: break-all;
}

.original-text {
    color: #ff4d4f;
    text-decoration: line-through;
}

.suggested-text {
    color: #52c41a;
}

.location {
    font-size: 12px;
    color: #999;
    margin-top: 8px;
    line-height: 1.4;
}

.location > div {
    margin-bottom: 2px;
}

.suggestion-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.accept-btn, .reject-btn {
    padding: 4px 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
}

.accept-btn {
    background-color: #52c41a;
    color: white;
}

.accept-btn:hover {
    background-color: #73d13d;
}

.reject-btn {
    background-color: #f5f5f5;
    color: #666;
}

.reject-btn:hover {
    background-color: #e8e8e8;
}
</style>

