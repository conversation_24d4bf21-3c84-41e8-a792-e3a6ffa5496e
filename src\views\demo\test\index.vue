<template>
    <div class="document-editor-wrapper">
        <div class="document-editor-container">
            <div id="documentEditor" v-show="isComponentReady"></div>
            <!-- 坐标高亮覆盖层 -->
            <div
                v-for="highlight in highlights"
                :key="highlight.id"
                class="coordinate-highlight"
                :style="{
                    left: highlight.x1 + 'px',
                    top: highlight.y1 + 'px',
                    width: (highlight.x2 - highlight.x1) + 'px',
                    height: (highlight.y2 - highlight.y1) + 'px',
                    display: highlight.visible ? 'block' : 'none'
                }"
            ></div>
            <div v-show="!isComponentReady" class="loading">
                <div class="spinner"></div>
                正在加载文档编辑器...
                <!-- 使用私有化部署的 OnlyOffice API -->
            </div>
        </div>
        <div class="suggestion-panel">
            <div class="suggestion-list">
                <a-button type="primary" @click="testJumpPage(10)">跳转页码</a-button>
                <a-button type="primary" @click="testHighlight(
                    {
                    page:2,position:[{
                    page: 3,
                    x1: 77, y1: 249, x2: 536, y2: 279
                }]})">跳转2页，第3页有高亮选区</a-button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { message } from 'ant-design-vue';
import CryptoJS from 'crypto-js';

// 声明 OnlyOffice 编辑器脚本环境中的全局对象
declare const Asc: any;
declare const Api: any;
declare const DocsAPI: any;

const isComponentReady = ref(false);
const docEditorInstance = ref<any>(null);
const connector = ref<any>(null);

// 坐标高亮数据
const highlights = ref<Array<{
    id: string;
    x1: number;
    y1: number;
    x2: number;
    y2: number;
    visible: boolean;
    page: number;
}>>([]);
// OnlyOffice 配置 - 与 docs/index.html 保持一致
const config = reactive({
    "height": "100%",
    "width": "100%",
    "document": {
        "fileType": "docx",
        "key": generateDocumentKey(),
        "title": "测试文件.docx",
        "url": "http://************:8090/files/test.docx",
    },
    "editorConfig": {
        "customization": {
            "autosave": false,
            "compactToolbar": true,
            "forcesave": true,
            "toolbarNoTabs": true,
            "help": false,
            "compactHeader": true,
            "hideRightMenu": true,
        },
        "mode": "edit",
        "callbackUrl": "http://172.16.0.198:18000/callback",
        "lang": "zh-CN",
    },
    "events": {
        "onDocumentReady": function () {
            console.log('OnlyOffice 应用已准备就绪');

            // 确保 docEditor 已创建
            if (!docEditorInstance.value) {
                console.error('docEditor 未初始化');
                return;
            }

            // 检查 createConnector 是否可用
            if (typeof docEditorInstance.value.createConnector !== 'function') {
                console.error('createConnector 不可用 —— 检查 api.js 版本 / Document Server edition / license');
                return;
            }

            // 在编辑器就绪后创建 connector
            connector.value = docEditorInstance.value.createConnector();
        },
        "onDocumentStateChange": function (event: any) {
            console.log('文档状态变更:', event);
        },
        "onError": function (event: any) {
            console.error('OnlyOffice错误:', event);
        },
        "onInfo": function (event: any) {
            console.log('OnlyOffice信息:', event);
        }
    },
    "token": "" as string // JWT token
});

// 生成文档密钥
function generateDocumentKey() {
    return 'demo_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}


// 新方案：使用OnlyOffice API进行页面跳转和高亮
const testJumpPage = (page: number) => {
    console.log(`开始跳转到第${page}页`);

    if (!connector.value) {
        message.error('编辑器连接器未就绪');
        return;
    }

    // 使用OnlyOffice API跳转页面
    (window as any).Asc = (window as any).Asc || {};
    (window as any).Asc.scope = (window as any).Asc.scope || {};
    (window as any).Asc.scope.targetPage = page;

    connector.value.callCommand(function () {
        try {
            var pageNum = parseInt(Asc.scope.targetPage, 10);
            var oDocument = Api.GetDocument();

            if (!oDocument) {
                console.error('无法获取文档对象');
                return;
            }

            console.log('尝试跳转到第' + pageNum + '页');

            // 方法1：尝试不同的页面索引方式
            var success = false;

            // 尝试从0开始的索引
            if (typeof oDocument.GoToPage === 'function') {
                try {
                    oDocument.GoToPage(pageNum - 1); // 0-based
                    console.log('使用GoToPage(pageNum-1)跳转: ' + (pageNum - 1));
                    success = true;
                } catch (e) {
                    console.warn('GoToPage(pageNum-1)失败:', e);
                }
            }

            // 如果上面失败，尝试从1开始的索引
            if (!success && typeof oDocument.GoToPage === 'function') {
                try {
                    oDocument.GoToPage(pageNum); // 1-based
                    console.log('使用GoToPage(pageNum)跳转: ' + pageNum);
                    success = true;
                } catch (e) {
                    console.warn('GoToPage(pageNum)失败:', e);
                }
            }

            // 方法2：通过段落索引精确跳转
            if (!success) {
                try {
                    var totalElements = oDocument.GetElementsCount();
                    console.log('文档总元素数: ' + totalElements);

                    // 更精确的元素索引计算
                    var elementsPerPage = Math.ceil(totalElements / 20); // 假设20页，可以调整
                    var targetElementIndex = Math.min((pageNum - 1) * elementsPerPage, totalElements - 1);

                    console.log('计算目标元素索引: ' + targetElementIndex + ' (每页约' + elementsPerPage + '个元素)');

                    var targetElement = oDocument.GetElement(targetElementIndex);
                    if (targetElement) {
                        // 选中目标元素来实现跳转
                        var range = targetElement.GetRange(0, Math.min(1, targetElement.GetText().length));
                        if (range && typeof range.Select === 'function') {
                            range.Select();
                            console.log('通过元素选择跳转成功，元素索引: ' + targetElementIndex);
                            success = true;
                        }
                    }
                } catch (e) {
                    console.warn('元素选择跳转失败:', e);
                }
            }

            // 方法3：逐步移动光标
            if (!success) {
                try {
                    if (typeof oDocument.MoveCursorToStartOfDocument === 'function') {
                        oDocument.MoveCursorToStartOfDocument();
                        console.log('光标移动到文档开始');

                        // 逐页移动
                        for (var i = 1; i < pageNum; i++) {
                            if (typeof oDocument.MoveCursorToNextPage === 'function') {
                                oDocument.MoveCursorToNextPage();
                                console.log('光标移动到第' + (i + 1) + '页');
                            } else {
                                // 如果没有NextPage方法，尝试移动段落
                                for (var j = 0; j < 5; j++) { // 每页假设5个段落
                                    if (typeof oDocument.MoveCursorToNextParagraph === 'function') {
                                        oDocument.MoveCursorToNextParagraph();
                                    }
                                }
                            }
                        }
                        success = true;
                        console.log('光标移动跳转完成');
                    }
                } catch (e) {
                    console.warn('光标移动跳转失败:', e);
                }
            }

            if (!success) {
                console.error('所有跳转方法都失败了');
            }

        } catch (e) {
            console.error('OnlyOffice API跳转失败:', e);
        }
    });

    message.success(`正在跳转到第${page}页...`);
};

// 新方案：使用OnlyOffice API在文档内部创建高亮标记
const testHighlight = (data: any) => {
    console.log('高亮数据:', data);

    // 处理按钮传入的数据格式: {page:2, position:[{page: 3, x1: 77, y1: 249, x2: 536, y2: 279}]}
    const { page, position } = data;

    if (!connector.value) {
        message.error('编辑器连接器未就绪');
        return;
    }

    // 先跳转到指定页面
    if (page) {
        testJumpPage(page);
    }

    // 延迟创建高亮，等待页面跳转完成
    setTimeout(() => {
        if (position && position.length > 0) {
            createDocumentHighlight(position[0]);
        }
    }, 2000);
};

// 在文档内部创建高亮标记
const createDocumentHighlight = (pos: any) => {
    const { page: highlightPage, x1, y1, x2, y2 } = pos;

    console.log(`准备在第${highlightPage}页坐标(${x1},${y1})-(${x2},${y2})创建文档内高亮`);

    // 清除之前的CSS高亮
    highlights.value = [];

    // 设置参数
    (window as any).Asc = (window as any).Asc || {};
    (window as any).Asc.scope = (window as any).Asc.scope || {};
    Object.assign((window as any).Asc.scope, {
        highlightPage,
        x1, y1, x2, y2
    });

    connector.value.callCommand(function () {
        try {
            var targetPage = parseInt(Asc.scope.highlightPage, 10);
            var oDocument = Api.GetDocument();

            if (!oDocument) {
                console.error('无法获取文档对象');
                return;
            }

            // 先跳转到目标页面
            if (typeof oDocument.GoToPage === 'function') {
                oDocument.GoToPage(targetPage - 1);
            }

            // 在文档中插入高亮标记
            var oNewParagraph = Api.CreateParagraph();
            var oRun = Api.CreateRun();
            oRun.AddText("🔴 高亮选区 - 坐标(" + Asc.scope.x1 + "," + Asc.scope.y1 + ")-(" + Asc.scope.x2 + "," + Asc.scope.y2 + ")");
            oRun.SetHighlight("red");
            oRun.SetColor(255, 255, 255); // 白色文字

            oNewParagraph.AddElement(oRun);

            // 设置段落边框
            oNewParagraph.SetLeftBorder("single", 24, 0, 255, 0, 0);
            oNewParagraph.SetRightBorder("single", 24, 0, 255, 0, 0);
            oNewParagraph.SetTopBorder("single", 24, 0, 255, 0, 0);
            oNewParagraph.SetBottomBorder("single", 24, 0, 255, 0, 0);

            // 插入到文档
            oDocument.Push(oNewParagraph);

            console.log('在文档中插入了高亮标记段落');

        } catch (e) {
            console.error('创建文档高亮失败:', e);
        }
    });

    message.success(`正在第${highlightPage}页创建高亮标记...`);
};


// JWT令牌生成
const setupJWT = () => {
    const payload = {
        document: config.document,
        editorConfig: config.editorConfig,
        height: config.height,
        width: config.width
    };

    // 使用与docs/index.html相同的密钥
    const secret = 'jwt@bos6688';
    config.token = createJWT(payload, secret);
};

/**
 * 生成 ONLYOFFICE 需要的 JWT（HS256）
 * @param {object} payload  ONLYOFFICE 配置对象
 * @param {string} secret   与 ONLYOFFICE 服务端一致的密钥
 * @returns {string} 标准 JWT
 */
const createJWT = (payload: any, secret: string): string => {
    if (!secret) return '';

    const header = { alg: 'HS256', typ: 'JWT' };
    const headerEnc = base64url(JSON.stringify(header));
    const payloadEnc = base64url(JSON.stringify(payload));

    const signature = CryptoJS.HmacSHA256(`${headerEnc}.${payloadEnc}`, secret);
    const signatureEnc = signature.toString(CryptoJS.enc.Base64)
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');

    return `${headerEnc}.${payloadEnc}.${signatureEnc}`;
};

/**
 * Base64URL编码
 * @param {string} data 要编码的数据
 * @returns {string} Base64URL编码后的字符串
 */
const base64url = (data: string): string => {
    // 使用CryptoJS进行Base64编码
    const base64 = CryptoJS.enc.Base64.stringify(
        CryptoJS.enc.Utf8.parse(data)
    );
    
    // 转换为Base64URL格式
    return base64
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
}; 

// 初始化编辑器
async function initEditor() {
    // 生成JWT令牌
    setupJWT();

    // 清除之前的编辑器实例
    if (docEditorInstance.value) {
        docEditorInstance.value.destroyEditor();
    }

    // 清空编辑器容器
    const container = document.getElementById('documentEditor');
    if (container) {
        container.innerHTML = '';
    }

    // 初始化新的编辑器
    docEditorInstance.value = new DocsAPI.DocEditor("documentEditor", config);

    isComponentReady.value = true;
}

onMounted(async () => {
    // 检查API是否加载成功
    if (typeof DocsAPI !== 'undefined') {
        await initEditor();
    } else {
        console.log('OnlyOffice API加载失败，请检查网络连接');
        message.error('OnlyOffice API加载失败，请检查网络连接');
    }
});

// 组件卸载时清理监听器
onUnmounted(() => { 
});
</script>

<style scoped>
/* 文档编辑器容器样式 */
.document-editor-container {
    width: 100%;
    height: 600px;
    border: 1px solid #ddd;
    position: relative;
}

#documentEditor {
    width: 100%;
    height: 100%;
}

/* 坐标高亮覆盖层样式 - 跟随文档缩放 */
.coordinate-highlight {
    position: absolute;
    background-color: rgba(255, 0, 0, 0.2); /* 透明红色背景 */
    border: 2px solid #ff0000; /* 红色边框 */
    pointer-events: none; /* 不阻挡鼠标事件 */
    z-index: 1000; /* 确保在最上层 */
    box-sizing: border-box;
    border-radius: 2px;

    /* 关键：让覆盖层跟随iframe内容缩放 */
    transform-origin: top left;
    /* 这个transform会通过JavaScript动态设置 */
}

.coordinate-highlight::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 0, 0, 0.1); /* 额外的透明红色层 */
}

.loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #666;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 2s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.test-buttons {
    margin: 20px 0;
    display: flex;
    gap: 10px;
}

.document-editor-wrapper {
    display: flex;
    width: 100%;
    height: 100vh;
}

.document-editor-container {
    flex: 1;
    height: 100vh;
    position: relative;
    border-right: 1px solid #e8e8e8;
}

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    font-size: 16px;
    color: #666;
}

#docEditor {
    width: 100%;
    height: 100%;
}

.suggestion-panel {
    width: 350px;
    height: 100vh;
    overflow-y: auto;
    background-color: #f9f9f9;
    padding: 16px;
    box-sizing: border-box;
}

.suggestion-panel h3 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 18px;
    color: #333;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 8px;
} 

.suggestion-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.suggestion-item {
    background-color: white;
    border-radius: 4px;
    padding: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.3s;
    border: 1px solid #e8e8e8;
}

.suggestion-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.suggestion-item.active {
    border-color: #1890ff;
    background-color: #e6f7ff;
}

.suggestion-content {
    margin-bottom: 12px;
}

.suggestion-title {
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.suggestion-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: normal;
}

.suggestion-status.pending {
    background-color: #fff7e6;
    color: #fa8c16;
    border: 1px solid #ffd591;
}

.suggestion-status.accepted {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.suggestion-status.rejected {
    background-color: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
}

.suggestion-text {
    font-size: 14px;
    color: #666;
}

.original-text, .suggested-text {
    margin-bottom: 4px;
    word-break: break-all;
}

.original-text {
    color: #ff4d4f;
    text-decoration: line-through;
}

.suggested-text {
    color: #52c41a;
}

.location {
    font-size: 12px;
    color: #999;
    margin-top: 8px;
    line-height: 1.4;
}

.location > div {
    margin-bottom: 2px;
}

.suggestion-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.accept-btn, .reject-btn {
    padding: 4px 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
}

.accept-btn {
    background-color: #52c41a;
    color: white;
}

.accept-btn:hover {
    background-color: #73d13d;
}

.reject-btn {
    background-color: #f5f5f5;
    color: #666;
}

.reject-btn:hover {
    background-color: #e8e8e8;
}
</style>

